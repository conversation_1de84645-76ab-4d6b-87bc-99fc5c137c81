\section{System Description}

\begin{figure*}[ht]
    \centering
    \includegraphics[width=1\textwidth]{sections/images/GUI.png}
    \caption{Screenshot of the SMS GUI running in Windows 11, displaying the results of a 32-cell, 5-minute MPPT trial.}
    \label{fig:gui}
\end{figure*}



At the core of each SMS module is a two-layer PCB (Fig.~\ref{fig:PhysicalSystem}\subref{fig:system_design_3}) hosting:
\begin{itemize}
  \item An Arduino microcontroller for sequencing and USB communication.
  \item 8x 12-bit Digital-to-Analog Converter (DAC) that supplies a programmable bias (0–3.3 V, 0.8 mV steps).
  \item 8x dual-channel Analog-to-Digital Converter (ADC) measuring voltage (–0.3–26 V, 4 mV resolution) and current (0–32 mA, 8 µA resolution).
\end{itemize}

Eight fully isolated channels allow JV sweeps or MPPT on all eight cells simultaneously. Each channel operates independently with its own dedicated DAC output and ADC input, preventing electrical crosstalk between measurements. This isolation ensures that the electrical characteristics of one cell do not influence measurements on adjacent channels, enabling true parallel testing.

The cell holder is a companion PCB that plugs into the main board via a 16-pin ribbon cable (two conductors per channel) plus two breadboard wires that power built-in LEDs for aligning masks. A 2.54 mm through-hole version of the SMS is also provided for users to create custom holder designs. Each cell makes contact through spring-loaded pogo pins in a 3D-printed frame (Fig.~\ref{fig:PhysicalSystem}\subref{fig:system_design_2}), ensuring consistent pressure on both electrodes. Using a 600 mm ribbon cable, each channel has a resistance of 2 $\Omega$ between the tip of the pogo pin and the SMS input/output. The 3D-printed frame also has a press-fit port for a 2.5 mm internal diameter N$_2$ line. Illumination uses an off-the-shelf LED grow light.

Power and data travel over a single USB cable to the host PC. Adding capacity is as simple as attaching additional SMS modules through USB (a powered hub is recommended when exceeding eight modules per computer USB port). We have verified stable parallel data collection from 8 SMS systems (64 cells) on a Windows 10/11 desktop; 12 GB is the minimum recommended RAM, and any modern CPU should suffice. However, the theoretical limit is estimated to be over 32 SMS systems measuring in parallel (256 cells). Note that current software support is limited to Windows 10/11.


\subsection{Graphical user interface}

Figure~\ref{fig:gui} shows our Python/PyQt GUI. Key features include:
\begin{itemize}
  \item Interactive configuration of scan parameters (voltage range, step size, sampling rate).
  \item Test queuing and automatic saving of test sequences.
  \item Real-time plotting of JV curves and MPPT power traces.
  \item Continuous CSV logging for robust data archival—even in case of a PC crash.
\end{itemize}
A comprehensive user guide is linked via our open-source repository.



\begin{table*}[htbp]
  \centering
  \caption{Agreement statistics for SMS vs.\ CAS (Each system with its respective holder).}
  \label{tab:agreement-stats}
  \begin{tabular*}{\textwidth}{@{\extracolsep{\fill}}l r r r r r}
    \toprule
    Metric                                       & Bias      & MAE      & RMSE    & 95\% CI (bias)           & 95\% Limits of Agreement \\
    \midrule
    \textbf{Fill factor}                         & $-0.0055$ & $0.0649$  & $0.1189$  & ($-0.0354$, $+0.0244$)   & ($-0.2401$, $+0.2290$)   \\
    \textbf{V\textsubscript{OC}} (V)             & $+0.0244$ & $0.0916$  & $0.2307$  & ($-0.0334$, $+0.0821$)   & ($-0.4288$, $+0.4775$)   \\
    \textbf{J\textsubscript{SC}} (mA\,cm$^{-2}$) & $+1.3222$ & $1.9173$  & $2.3526$  & ($+0.8323$, $+1.8121$)   & ($-2.5218$, $+5.1663$)   \\
    \textbf{PCE (\%)}                            & $+0.4628$ & $1.9145$  & $2.6241$  & ($-0.5572$, $+1.4827$)   & ($-4.6928$, $+5.6184$)   \\
    \bottomrule
  \end{tabular*}
\end{table*}


\begin{table*}[htbp]
  \centering
  \caption{Agreement statistics for SMS vs.\ CAS (CAS holder on both systems).}
  \label{tab:agreement-stats2}
  \begin{tabular*}{\textwidth}{@{\extracolsep{\fill}}l r r r r r}
    \toprule
    Metric                                       & Bias      & MAE      & RMSE    & 95\% CI (bias)            & 95\% Limits of Agreement \\
    \midrule
    \textbf{Fill factor}                         & $-0.0221$ & $0.0529$ & $0.0694$ & $(-0.0389, -0.0052)$      & $(-0.1522, +0.1081)$      \\
    \textbf{V\textsubscript{OC}} (V)             & $+0.0049$ & $0.0158$ & $0.0283$ & $(-0.0022, +0.0121)$      & $(-0.0502, +0.0600)$      \\
    \textbf{J\textsubscript{SC}} (mA\,cm$^{-2}$) & $-0.3306$ & $0.4229$ & $0.7264$ & $(-0.4962, -0.1650)$      & $(-1.6086, +0.9474)$     \\
    \textbf{PCE (\%)}                            & $-0.4478$ & $0.9822$ & $1.6322$ & $(-1.0439, +0.1483)$      & $(-3.5767, +2.6811)$     \\
    \bottomrule
  \end{tabular*}
\end{table*}

\begin{table*}[htbp]
  \centering
  \caption{Platform repeatability statistics for fill factor and power.}
  \label{tab:platform-repeatability}
  \begin{tabular*}{\textwidth}{@{\extracolsep{\fill}}l r r}
    \toprule
    Metric                                    & \multicolumn{1}{c}{Fill factor (units)} & \multicolumn{1}{c}{Power (mW, MPPT derived)} \\
    \midrule
    Repeatability $\sigma_r$                  & 0.0087                                   & 0.0331                          \\
    RSD (\%)                                  & 1.26\%                                   & 0.34\%                          \\
    95\% Repeatability Coefficient            & 0.0241                                   & 0.0918                          \\
    \bottomrule
  \end{tabular*}
\end{table*}


\begin{figure*}[ht]
  \centering
  %--- First row of subfigures
  \begin{subfigure}[b]{0.45\textwidth}
    \centering
    \includegraphics[width=\textwidth]{sections/images/Comparison_FF_BoxPlot.png}
    \caption{}
    \label{fig:subfig_measurement1}
  \end{subfigure}
  \hfill
  \begin{subfigure}[b]{0.45\textwidth}
    \centering
    \includegraphics[width=\textwidth]{sections/images/Comparison_PCE_BoxPlot.png}
    \caption{}
    \label{fig:subfig_measurement2}
  \end{subfigure}

  %--- Second row of subfigures
  \begin{subfigure}[b]{0.47\textwidth}
    \centering
    \includegraphics[width=\textwidth]{sections/images/JV_Plot.png}
    \caption{}
    \label{fig:subfig_measurement3}
  \end{subfigure}
  \hfill
  \begin{subfigure}[b]{0.47\textwidth}
    \centering
    \includegraphics[width=\textwidth]{sections/images/MPPT_plot.png}
    \caption{}
    \label{fig:subfig_measurement4}
  \end{subfigure}

  \caption{%
    (a)~Box plot of SMS vs CAS Fill Factor on all 4 devices, with forward and reverse JV scans separated.
    (b)~Box plot of SMS vs CAS PCE on all 4 devices
    (c)~JV scan of one device from SMS plotted against CAS. Dotted traces represent SMS while solid traces represent CAS.
    (d)~MPPT plot of one device from SMS plotted against CAS. Dotted traces represent SMS while solid traces represent CAS.
  }
  \label{fig:measurementResults}
\end{figure*}

\begin{figure*}[ht]
  \centering
  \includegraphics[width=1\textwidth]{sections/images/LONG.png}
  \caption{200-hour stability measurement.}
  \label{fig:stability}
\end{figure*}

\subsection{JV Scan}

To characterize initial performance and detect hysteresis, the system conducts a bidirectional voltage sweep (0–3.3 V). At each step, it averages multiple readings to reduce noise. The system then computes open-circuit voltage (V\textsubscript{OC}) and maximum-power-point voltage (V\textsubscript{MPP}). Configurable parameters are:

\begin{enumerate}
    \item \textbf{Scan Range:} The voltage span for the scan (0–3.3 V).
    \item \textbf{Scan Step Size:} The voltage increment between readings (0.001–1 V).
    \item \textbf{Cell Area:} Active area of the solar cell (in cm$^2$), used to calculate current density.
    \item \textbf{Scan Read Count:} Number of readings averaged per voltage step to reduce noise.
    \item \textbf{Scan Rate:} The speed of the voltage sweep.
\end{enumerate}


\subsection{MPPT Algorithm}

We implement a perturb-and-observe (P\&O) tracker \cite{esram2007comparison}. Initial voltage may be set manually or automatically—using a prior JV scan to set the start voltage at 85\% of V\textsubscript{OC} \cite{Dai2022}. Configurable parameters are:

\begin{enumerate}
    \item \textbf{Starting Voltage:} The initial load voltage (0–3.3 V) (does not apply if scan is conducted beforehand).
    \item \textbf{Starting Percentage:}: The percentage of V\textsubscript{OC} to set the starting voltage (only applies if scan is conducted beforehand).
    \item \textbf{Step Size:} The voltage increment for adjustments (0.001–1 V).
    \item \textbf{Cell Area:} The active area of the solar cell (in cm$^2$) for live PCE calculations.
    \item \textbf{Measurements Per Step:} The number of readings per voltage level that are averaged for noise reduction.
    \item \textbf{Measurement Delay:} The delay between setting voltage and measurement, which allows the cell to stabilize (ms).
    \item \textbf{Measurement Interval:} The time interval across which measurements are taken per voltage step (ms).
    \item \textbf{Measurement Time:} The total duration for the MPPT process (specified in minutes or hours).
\end{enumerate}

P\&O was chosen because it facilitates straightforward implementation with low computational demand, which is ideal for Arduino-based systems. In contrast to methods like incremental conductance or fuzzy logic control, P\&O does not require extensive calibration or complex algorithms, improving reliability. However, since our experiments are conducted under stable laboratory conditions, the risk of common P\&O issues—such as oscillation around the MPP or convergence to local minima, which are common in more dynamic environments—is minimal. This makes P\&O not only reliable but also a highly practical choice for our specific testing setup \cite{esram2007comparison}.


\subsection{Cost and Assembly}

The entire SMS system can be factory-fabricated and assembled by JLCPCB with a minimum order quantity of five per board for under \$275 total (excluding shipping and duties). Full assembly instructions and design files are provided in the open-source repository.
