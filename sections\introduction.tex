

\begin{figure*}[ht]
    \centering
    \begin{subfigure}[b]{0.45\textwidth}
        \includegraphics[width=\textwidth]{sections/images/ENTIRE.png}
        \caption{}
        \label{fig:system_design_1}
    \end{subfigure}
    \hfill
    \begin{subfigure}[b]{0.45\textwidth}
        \includegraphics[width=\textwidth]{sections/images/CELL.png}
        \label{fig:system_design_2}
        \caption{}
    \end{subfigure}
    \begin{subfigure}[b]{0.45\textwidth}
        \includegraphics[width=\textwidth]{sections/images/pcb.png}
        \caption{}
        \label{fig:system_design_3}
    \end{subfigure}
    \hfill
    \begin{subfigure}[b]{0.45\textwidth}
        \includegraphics[width=\textwidth]{sections/images/PROBE.png}
        \label{fig:system_design_4}
        \caption{}
    \end{subfigure}
    \caption{Physical overview of the Stability Measurement System (SMS). a) Overall system block diagram. Red arrows indicate data and power connections. b) Detail of the pogo-pin contacts in the 3D-printed enclosure. c) Main PCB assembly: eight independent, electrically isolated measurement channels. d)Sample holder mating with eight cells via spring-loaded pogo pins.}
    \label{fig:PhysicalSystem}
\end{figure*}

\section{Introduction}
Perovskite solar cells (PSCs) have rapidly advanced from their initial \mbox{$\sim$4\%} power-conversion efficiency (PCE) reported in 2009 to exceeding 25.5\% within a decade, rivaling silicon while offering simpler and more cost-effective manufacturing processes.\cite{Snaith2013,cheng2021pushing} Despite these remarkable achievements, inadequate stability remains a critical roadblock to commercialization. Environmental stressors such as illumination, moisture, oxygen, thermal fluctuations, and electrical bias can induce ion migration, interfacial reactions, and phase segregation, all of which degrade device performance.\cite{perini2021pressing,zhou2022recent,zhang2022degradation,liu2020uv,dipta2021stability}

Instrumentation capable of simultaneously applying these stress conditions and performing rigorous electrical measurements is typically expensive: commercial stability analyzers cost between US\$10,000–60,000.\cite{keesey2023opensource} Even "low-cost" commercially available devices cost thousands of dollars and can only measure one cell at a time.\cite{Ossila_IV_System_2025} Such high costs restrict research groups from fully exploring combinatorial compositional spaces or obtaining statistically significant device population data.

Open-source hardware represents a viable approach to making high-throughput stability testing more accessible. Recent community-driven initiatives have delivered affordable environmental chambers, LED-based solar simulators, and optical proxy monitoring systems.\cite{keesey2023opensource,zhang2025advancing,Samir2020LED} While a few low-cost and open-source, single-channel current-voltage (JV) curve and maximum power point tracking (MPPT) testers exist, these systems are difficult to reproduce without domain knowledge.\cite{Papageorgas2015_IVTracer, csatt2017_IVSwinger2, ElHammoumi2020_RealTimeDAQ} Currently, no open-source implementation exists for an instrument capable of performing automated multi-cell parallel MPPT and JV measurements.

In this work, we introduce a \$100 Arduino-based Stability Measurement System (SMS) designed to bridge this gap. Each SMS module integrates a custom printed circuit board (PCB), sample holder, 3D-printed enclosure, and pre-built Python-based control software to perform parallel JV and MPPT measurements on eight independent pixels. Multiple modules can be connected, allowing a single Windows computer to supervise over 250 cells in parallel, compared to the single channel to which previous cost-effective systems are limited. Only basic 3D printing and computer skills are required to assemble the SMS.

The SMS offers the core functionality of commercial analyzers at roughly 1\% of the cost. We benchmark the SMS against one of these commercially available systems (CAS), demonstrate reliable operation over 200 hours, and release all hardware and software under an open license to facilitate community adoption and further innovation.

