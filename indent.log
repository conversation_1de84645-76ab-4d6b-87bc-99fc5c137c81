INFO:  latexindent.pl version 3.24.5, 2025-03-13, a script to indent .tex files
       latexindent.pl lives here: C:/Users/<USER>/AppData/Local/Programs/MiKTeX/scripts/latexindent/
       Wed Jun 25 12:22:55 2025
       Filename: c:/Users/<USER>/Dropbox/code/A Cost-Effective Arduino-Based System for Measuring Solar Panel Stability/sections/__latexindent_temp_results_and_discussion.tex
INFO:  Processing switches:
       -y|--yaml: YAML settings specified via command line
       -c|--cruft: cruft directory
INFO:  Directory for backup files and log file c:\Users\<USER>\Dropbox\code\A Cost-Effective Arduino-Based System for Measuring Solar Panel Stability\indent.log:
       c:\Users\<USER>\Dropbox\code\A Cost-Effective Arduino-Based System for Measuring Solar Panel Stability\
INFO:  Perl modules are being loaded from the following directories:
       C:/Strawberry/perl/lib/FindBin.pm
       C:/Strawberry/perl/vendor/lib/YAML/Tiny.pm
       C:/Strawberry/perl/lib/File/Copy.pm
       C:/Strawberry/perl/lib/File/Basename.pm
       C:/Strawberry/perl/lib/Getopt/Long.pm
       C:/Strawberry/perl/vendor/lib/File/HomeDir.pm
INFO:  LatexIndent perl modules are being loaded from, for example:
       C:/Users/<USER>/AppData/Local/Programs/MiKTeX/scripts/latexindent/LatexIndent/Document.pm
INFO:  YAML settings read: defaultSettings.yaml
       Reading defaultSettings.yaml from C:/Users/<USER>/AppData/Local/Programs/MiKTeX/scripts/latexindent/defaultSettings.yaml
INFO:  YAML reading settings
       Home directory is C:\Users\<USER>