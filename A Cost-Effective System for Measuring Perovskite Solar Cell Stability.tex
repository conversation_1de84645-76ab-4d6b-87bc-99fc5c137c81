% \documentclass[onecolumn,10pt]{IEEEtran}
\documentclass[twocolumn,10pt]{article}

\usepackage{graphicx}
\usepackage{amsmath}
\usepackage{hyperref}
\usepackage{cite}
\usepackage{subcaption}
\usepackage{authblk}
\usepackage{indentfirst}
\usepackage[letterpaper, margin=1in]{geometry}
\usepackage{booktabs}

\title{A Cost-Effective System for Measuring Perovskite Solar Cell Stability}
\author[ ]{<PERSON>}
\author[1]{<PERSON>}
\author[1]{<PERSON><PERSON><PERSON>}
\author[1]{<PERSON>}
\author[1]{<PERSON><PERSON>}
\author[1]{<PERSON><PERSON><PERSON>}
\usepackage{lineno}

% allow up to 3 double‐column floats at the top of a page
\setcounter{dbltopnumber}{3}

% allow them to occupy up to 80% of the page height
\renewcommand{\dbltopfraction}{.8}

% make sure LaTeX can still put text on that page
\renewcommand{\textfraction}{.1}
\renewcommand{\floatpagefraction}{.75}

% TODO: talk about difference in silver paste
% moving average the long trace
%  - in the introduction add a couple of lines on what has been done on equipment design in research.
% talk about difference in litos holder and my design

\affil[1]{School of Materials Science and Engineering, School of Chemistry and Biochemistry, Center for Organic Photonics and Electronics, Georgia Institute of Technology, North Ave NW, Atlanta, Georgia 30332, USA}

\date{}

\begin{document}
% \linenumbers

\maketitle

\newpage

\begin{abstract}


Reliable assessment of long term stability remains a critical barrier to the commercialization of perovskite photovoltaics, yet existing measurement platforms cost tens of thousands of dollars and typically evaluate only a few devices simultaneously. Here, we introduce an open source, Arduino based Stability Measurement System (SMS) capable of performing parallel current-voltage and maximum power point tracking measurements on eight cells simultaneously, at a per module cost below \$100. The modular hardware comprises a custom printed-circuit board and a plug-and-play sample holder, allowing linear scaling; a single Windows computer can measure more than 250 cells in parallel. A Python graphical interface automates experiment queues, logs data in real time, and saves ready to plot datasets. Benchmarking against a commercial analyzer demonstrates SMS accuracy within ±1 percentage-point absolute power-conversion efficiency under AM1.5G illumination and stable, uninterrupted operation during 200-hour stability tests. By significantly lowering financial and technical barriers for high-throughput ageing studies, the SMS will accelerate device optimization efforts across the perovskite research community.All bills of materials, assembly instructions, and operating codes are available in open-source repositories.

\end{abstract}

\input{sections/introduction.tex}

\input{sections/system_description.tex}

\input{sections/experimental.tex}

\input{sections/results_and_discussion.tex}

\section{Conclusion}
We have successfully developed and validated an Arduino-based Stability Measurement System (SMS) that addresses the critical need for accessible, high-throughput perovskite solar cell stability testing. At approximately \$100 per 8-channel module, the SMS delivers core measurement functionality at roughly 1\% of the cost of commercial analyzers (\$10,000–60,000), while enabling parallel testing of up to 250+ cells compared to the single-channel limitation of existing cost-effective systems.

Validation against commercial systems demonstrated acceptable measurement accuracy, with the SMS achieving relative standard deviations of 1.26\% for fill factor and 0.34\% for power conversion efficiency under controlled conditions. While still lacking in accuracy and precision compared to these commercial systems, the system excels in its intended role as a cheap screening platform for comparative stability studies. The successful 200-hour degradation test confirms the SMS's reliability for extended monitoring under laboratory conditions.

The open-source design, requiring only basic 3D printing and computer skills for assembly, democratizes access to multi-channel stability testing. This accessibility enables research groups with limited budgets to explore combinatorial compositional spaces and obtain statistically significant device population data—capabilities previously restricted by the high cost of commercial instrumentation. By removing financial barriers to high-throughput aging studies, the SMS accelerates research and development in perovskite solar technologies, bringing the field closer to large-scale commercial viability.

\newpage
\bibliographystyle{IEEEtran}
\bibliography{references}

\end{document}
