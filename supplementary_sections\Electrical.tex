
\section{Electrical Schematics and PCB Layout}

% GOOD REFERENCE FOR COMPELX DESIGN
% https://pdf.sciencedirectassets.com/277910/1-s2.0-S1876610215X00142/1-s2.0-S1876610215014095/main.pdf?X-Amz-Security-Token=IQoJb3JpZ2luX2VjEOT%2F%2F%2F%2F%2F%2F%2F%2F%2F%2FwEaCXVzLWVhc3QtMSJIMEYCIQDB98U9JS4xkMFwHSP8xpl6S3WwgDsX%2BIEsW8zfDhAgSAIhAM7N67xXxyjjcjc8uP0EB89tedaI1mgHs0j4aLK%2BhcVMKrEFCH0QBRoMMDU5MDAzNTQ2ODY1IgzC%2B9Pr8Eyt0PK9oCAqjgV9gNHlyzkbxbrudQP%2B%2FDhbkqWUEwAtSLtwWQbN3xNyc4jynGkazuuxQJfyt%2BanVTZ59bJoeLfmckleNuX2aEu13c%2FQo2bmEiX1%2BrJKd4IOkjpZiiCccJgv7aB3WaPGVHGCcxa5xkxq64mvtmXNxwyqNbjHPsvUYoHbry6lU%2FptZRmRJ7yOQBshkJTnfwRzE0G7qZpx2ovXNIm010sD2siDGfecMDj%2BCSyuOA%2B45KtiF8N%2Fj9pVQUG7zjqCeqSCHjmZ5z92C5%2FXlXDVgu7iJjXUjrbpdgthOmLl9J%2FS0OBODJFRpnSBZI7br1bxOP1lQND%2FtwuLBMpHPrmKFh9EsK63EzhBF1gKu0Otj%2BqXGoIU6Tblo88nEMadfn4OPUShdaxO%2B%2BlY5%2FI7mk%2FHIy7XhsPdpWQAORjOWvL3TB7r3uKJ%2FSychEbukmjtqeUi%2BbUHSB1b2IhW8%2Bb5GuK3ASeuzXOLug%2BcY0rjSBZyDYTMbQd5JvUcGl4cGXUBmC%2BJlRVOgTDiNKpmt3dRiS%2B1jFU1RNBhK4o0Ra6meEXC8YzkddWVhVi08NrVAFXsMpohBCxV6lY7Mxr6takg8jG63kO0vSEDzrNqO9WMfnMcpOmS5vInTbInDGlNmapCajO1fMFFy%2ByeR%2FWxtDAw%2FqWZQnNpWsqtlTmyg0ncETXIkXVT8%2BgLndDRXH%2BRz%2FUr4xTTECwFTkRUMLvlG4WgoeByxhcC3vdzXdZzFNSK64Mp1pT9155o6BppMdvIM6touQk4NGnASF8JHviUa7fkWRTmxDezlIjKVB3UP8TXP8hWdzd%2BQKbAjBaIm0zK0qH%2FCXmH0UAWi8lz1poi3bgpEK7xEo7lvwHeZevuyKEp2Jz3ckVbbPYw37C%2FwAY6sAGzUFOGDtPtfUIfbjK4iZ5q8pPxCPyNK2ehaT73WFQRqtuuGGd9z%2B%2FfBZ9yv74pRHwAB%2FXmRlDnIlFz87yCviOPzSy%2FnaJERb4WrCnulvmTRzzThxa%2B2tTBkRysrltWbqCqUJRqMuPIgxTVY%2BiS4DhbdYUXhwM3nSOOVY0ps4Fs83FJ36KZ5kDlaja6mbhrQFz%2FGiGvZ28EzbAld4Nh8PpNR94zy0I0I27mJ8yckO%2F0Og%3D%3D&X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Date=20250428T200925Z&X-Amz-SignedHeaders=host&X-Amz-Expires=300&X-Amz-Credential=ASIAQ3PHCVTY3BC655JE%2F20250428%2Fus-east-1%2Fs3%2Faws4_request&X-Amz-Signature=fb6430f912cff88f32330ad42c24c5b7ad9e3098d87a807abc807f4a8a0851eb&hash=da4b77759521b9e5861623bde5a69eeb51e7fce63f583efeb4c9ea4cf610a4d2&host=68042c943591013ac2b2430a89b270f6af2c76d8dfd086a07176afe7c76c2c61&pii=S1876610215014095&tid=spdf-daa177bb-f650-4e74-8ac7-30856f95b256&sid=6794535b15ea924c1019dfb03678cb873042gxrqa&type=client&tsoh=d3d3LnNjaWVuY2VkaXJlY3QuY29t&rh=d3d3LnNjaWVuY2VkaXJlY3QuY29t&ua=0f155957565a5450540653&rr=9379327a4e539c5e&cc=us

\subsection{SMS Main Board}

\begin{figure*}[ht]
    \centering
    % Row 1
    \begin{subfigure}[b]{0.45\textwidth}
        \includegraphics[width=\textwidth]{supplementary_sections/images/circuit.png}
        \caption{Simplified circuit diagram for a single cell assembly. (REPLACE with correct solar cell symbol)}
        \label{fig:simplecircuit}
    \end{subfigure}
    \hfill
    \begin{subfigure}[b]{0.45\textwidth}
        \includegraphics[width=\textwidth]{supplementary_sections/images/SCHEMATIC.png}
        \caption{Schematic for measuring one cell.}
        \label{fig:schematic}
    \end{subfigure}

    \vspace{1em} % space between rows

    % Row 2
    \begin{subfigure}[b]{0.45\textwidth}
        \includegraphics[width=\textwidth]{supplementary_sections/images/circuit.png}
        \caption{(Placeholder) Detailed view of the PCB layout.}
        \label{fig:pcb_layout}
    \end{subfigure}
    \hfill
    \begin{subfigure}[b]{0.45\textwidth}
        \includegraphics[width=\textwidth]{supplementary_sections/images/circuit.png}
        \caption{(Placeholder) Measurement probe configuration.}
        \label{fig:probe_config}
    \end{subfigure}

    \caption{Electrical diagrams and component layouts of the stability setup.}
    \label{fig:circuit}
\end{figure*}


Designed in EasyEDA and assembled by JLCPCB, the board is fully open-sourced—including both schematics and PCB layout. It features eight sub-circuits (one for each pixel) that each use two integrated circuits(IC).

\begin{itemize}
    \item \textbf{MCP4725 DAC} for load application (I$^2$C addresses 0x60 to 0x67).
    \item \textbf{INA219 ADC} for voltage and amperage monitoring (I$^2$C addresses 0x40 to 0x47).
\end{itemize}

In Figure, the DAC (P0) applies a load voltage (0-3.3 V with 0.8 mV resolution) to simulate varying conditions. The high-side ADC (I0) measures voltage and current via a $10\Omega$ (R3) shunt resistor, enabling current measurements across 0-32 mA with an 8 µA resolution. The capacitors (C1, C2, C3) are used to help stabilize power fluctuations in the voltage input. The inputs and outputs of this circuit are controlled by the ANE over the I$^2$C communication protocol. The use of I$^2$C allows 8 DAC/ADC units to be connected to each board which allows parallel testing.

Both the MCP4725 and INA219 were selected for their affordability and ease of use. In addition, readily available C++ libraries from Adafruit facilitated their integration. Moreover, the voltage and current measurement ranges of these components were well-suited to the experiment’s requirements. Alternatively, other components can be substituted to achieve higher measurement accuracy or to handle higher voltage and current levels.

\section{Gerber and 3D Enclosure Files}
\label{sec:gerber}
All Gerber files, drill files, and STL models are provided in the GitHub repository:
\url{https://github.com/yourrepo/SMS}.