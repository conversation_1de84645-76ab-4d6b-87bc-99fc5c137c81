
\section{Additional GUI Screenshots and Features}


Figure is the Stability Setup GUI. The Python-based GUI, built with PySide6, is designed to facilitate use of the Stability Setup Hardware. This intuitive interface simplifies system operation and highlights the performance metrics that are critical for evaluating solar panel stability. Here are the functions of the GUI:

\begin{enumerate}
    \item \textbf{Set Up Tests:} Enter details like trial names, email notifications, measurement parameters, etc.
    \item \textbf{Manage Settings:} Save presets to easily switch between best configurations.
    \item \textbf{Visualize Data:} Built in live plotting of JV and MPPT curves.
    \item \textbf{Manage Hardware:} Check how many stability setup systems are connected and configure their IDs.
\end{enumerate}
